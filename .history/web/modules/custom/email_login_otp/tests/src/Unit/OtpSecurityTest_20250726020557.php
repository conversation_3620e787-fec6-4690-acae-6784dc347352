<?php

namespace Drupal\Tests\email_login_otp\Unit;

use Drupal\Tests\UnitTestCase;
use Drupal\email_login_otp\Services\Otp;
use Drupal\Core\Config\ConfigFactory;
use Drupal\Core\Database\Connection;
use <PERSON>upal\Core\Extension\ExtensionPathResolver;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Password\PasswordInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Render\RendererInterface;
use Drupal\Core\TempStore\PrivateTempStoreFactory;
use Drupal\Core\Flood\FloodInterface;

/**
 * Tests for OTP security fixes.
 *
 * @group email_login_otp
 */
class OtpSecurityTest extends UnitTestCase {

  /**
   * Test that OTP generation uses secure random numbers.
   */
  public function testSecureOtpGeneration() {
    // Mock dependencies.
    $database = $this->createMock(Connection::class);
    $mailManager = $this->createMock(MailManagerInterface::class);
    $languageManager = $this->createMock(LanguageManagerInterface::class);
    $hasher = $this->createMock(PasswordInterface::class);
    $tempStoreFactory = $this->createMock(PrivateTempStoreFactory::class);
    $configFactory = $this->createMock(ConfigFactory::class);
    $extensionPathResolver = $this->createMock(ExtensionPathResolver::class);
    $renderer = $this->createMock(RendererInterface::class);
    $flood = $this->createMock(FloodInterface::class);

    // Create OTP service instance.
    $otpService = new Otp(
      $database,
      $mailManager,
      $languageManager,
      $hasher,
      $tempStoreFactory,
      $configFactory,
      $extensionPathResolver,
      $renderer,
      $flood
    );

    // Test that flood control methods exist and are callable.
    $this->assertTrue(method_exists($otpService, 'isOtpVerificationAllowed'));
    $this->assertTrue(method_exists($otpService, 'registerFailedOtpVerification'));
    $this->assertTrue(method_exists($otpService, 'clearOtpVerificationFlood'));
  }

  /**
   * Test flood control functionality.
   */
  public function testFloodControlMethods() {
    // Mock flood service.
    $flood = $this->createMock(FloodInterface::class);
    
    // Test that flood control allows verification initially.
    $flood->expects($this->any())
      ->method('isAllowed')
      ->willReturn(TRUE);

    // Mock other dependencies.
    $database = $this->createMock(Connection::class);
    $mailManager = $this->createMock(MailManagerInterface::class);
    $languageManager = $this->createMock(LanguageManagerInterface::class);
    $hasher = $this->createMock(PasswordInterface::class);
    $tempStoreFactory = $this->createMock(PrivateTempStoreFactory::class);
    $configFactory = $this->createMock(ConfigFactory::class);
    $extensionPathResolver = $this->createMock(ExtensionPathResolver::class);
    $renderer = $this->createMock(RendererInterface::class);

    // Mock config to return default values.
    $config = $this->createMock(\Drupal\Core\Config\Config::class);
    $config->expects($this->any())
      ->method('get')
      ->willReturnMap([
        ['otp_user_limit', 5],
        ['otp_user_window', 3600],
        ['otp_ip_limit', 10],
        ['otp_ip_window', 3600],
      ]);
    
    $configFactory->expects($this->any())
      ->method('get')
      ->with('email_login_otp.config')
      ->willReturn($config);

    // Create OTP service instance.
    $otpService = new Otp(
      $database,
      $mailManager,
      $languageManager,
      $hasher,
      $tempStoreFactory,
      $configFactory,
      $extensionPathResolver,
      $renderer,
      $flood
    );

    // Test that verification is allowed when flood control permits.
    $this->assertTrue($otpService->isOtpVerificationAllowed(1, '127.0.0.1'));
  }

}
