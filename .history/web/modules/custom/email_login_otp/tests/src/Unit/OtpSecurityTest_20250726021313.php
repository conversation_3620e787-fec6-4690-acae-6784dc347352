<?php

namespace Drupal\Tests\email_login_otp\Unit;

use Drupal\Tests\UnitTestCase;
use Drupal\email_login_otp\Services\Otp;
use Drupal\Core\Config\ConfigFactory;
use Drupal\Core\Database\Connection;
use <PERSON>upal\Core\Extension\ExtensionPathResolver;
use Drupal\Core\Mail\MailManagerInterface;
use Drupal\Core\Password\PasswordInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Render\RendererInterface;
use Drupal\Core\TempStore\PrivateTempStoreFactory;

/**
 * Tests for OTP security fixes.
 *
 * @group email_login_otp
 */
class OtpSecurityTest extends UnitTestCase {

  /**
   * Test that OTP service can be instantiated without flood control.
   */
  public function testOtpServiceInstantiation() {
    // Mock dependencies.
    $database = $this->createMock(Connection::class);
    $mailManager = $this->createMock(MailManagerInterface::class);
    $languageManager = $this->createMock(LanguageManagerInterface::class);
    $hasher = $this->createMock(PasswordInterface::class);
    $tempStoreFactory = $this->createMock(PrivateTempStoreFactory::class);
    $configFactory = $this->createMock(ConfigFactory::class);
    $extensionPathResolver = $this->createMock(ExtensionPathResolver::class);
    $renderer = $this->createMock(RendererInterface::class);

    // Create OTP service instance.
    $otpService = new Otp(
      $database,
      $mailManager,
      $languageManager,
      $hasher,
      $tempStoreFactory,
      $configFactory,
      $extensionPathResolver,
      $renderer
    );

    // Test that the service was created successfully.
    $this->assertInstanceOf(Otp::class, $otpService);

    // Test that basic methods exist.
    $this->assertTrue(method_exists($otpService, 'generate'));
    $this->assertTrue(method_exists($otpService, 'check'));
    $this->assertTrue(method_exists($otpService, 'send'));
  }

}
