<?php

namespace Drupal\email_login_otp\Services;

use Drupal\Core\Config\ConfigFactory;
use <PERSON>upal\Core\Database\Connection;
use <PERSON>upal\Core\Extension\ExtensionPathResolver;
use <PERSON><PERSON>al\Core\Mail\MailManagerInterface;
use <PERSON><PERSON>al\Core\Password\PasswordInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Render\RendererInterface;
use Drupal\Core\TempStore\PrivateTempStoreFactory;

/**
 * Otp service class.
 */
class Otp {
  /**
   * The database object.
   *
   * @var \Drupal\Core\Database\Connection
   */
  protected $database;
  /**
   * The database object.
   *
   * @var \Drupal\Core\Config\ConfigFactory
   */
  protected $configFactory;
  /**
   * The database object.
   *
   * @var \Drupal\Core\Extension\ExtensionPathResolver
   */
  protected $extensionPathResolver;
  /**
   * The mailManager object.
   *
   * @var \Drupal\Core\Mail\MailManagerInterface
   */
  protected $mailManager;
  /**
   * The languageManager object.
   *
   * @var \Drupal\Core\Language\LanguageManagerInterface
   */
  protected $languageManager;
  /**
   * The username.
   *
   * @var string
   */
  private $username;
  /**
   * The tempoStorage object.
   *
   * @var \Drupal\Core\TempStore\PrivateTempStoreFactory
   */
  private $tempStorageFactory;

  /**
   * @var \Drupal\Core\Password\PasswordInterface
   */
  private PasswordInterface $hasher;

  /**
   * @var \Drupal\Core\Render\RendererInterface
   */
  private RendererInterface $renderer;

  /**
   * @var \Drupal\Core\Flood\FloodInterface
   */
  private FloodInterface $flood;

  /**
   * Constructor of the class.
   */
  public function __construct(Connection $connection, MailManagerInterface $mail_manager, LanguageManagerInterface $language_manager, PasswordInterface $hasher, PrivateTempStoreFactory $tempStoreFactory, ConfigFactory $config_factory, ExtensionPathResolver $extension_path_resolver, RendererInterface $renderer, FloodInterface $flood) {
    $this->tempStorageFactory = $tempStoreFactory;
    $this->database           = $connection;
    $this->configFactory      = $config_factory;
    $this->extensionPathResolver = $extension_path_resolver;
    $this->mailManager        = $mail_manager;
    $this->languageManager    = $language_manager;
    $this->hasher             = $hasher;
    $this->renderer           = $renderer;
    $this->flood              = $flood;
  }

  /**
   * Generates a new OTP.
   */
  public function generate($username) {
    $this->username = $username;
    $uid = $this->getUserField('uid');
    $this->tempStorageFactory->get('email_login_otp')->set('uid', $uid);

    if ($this->exists($uid)) {
      return $this->update($uid);
    }
    return $this->new($uid);
  }

  /**
   * Sends the OTP to user via email.
   */
  public function send($otp, $to = NULL) {
    $to                = $to ? $to : $this->getField('email');
    $langcode          = $this->languageManager->getCurrentLanguage()->getId();
    $mail_render = [
      '#theme' => 'email_login_otp_mail',
      "#otp"   => $otp,
      '#username' => $this->username,
    ];
    $mail_message     = $this->renderer->render($mail_render);
    $params['message'] = $mail_message;
    return $this->mailManager->mail('email_login_otp', 'email_login_otp_mail', $to, $langcode, $params, NULL, TRUE);
  }

  /**
   * Checks if the given OTP is valid.
   */
  public function check($uid, $otp) {
    if ($this->exists($uid)) {
      $select = $this->database->select('email_login_otp', 'u')
        ->fields('u', ['otp', 'expiration'])
        ->condition('uid', $uid, '=')
        ->execute()
        ->fetchAssoc();
      if ($select['expiration'] >= time() && $this->hasher->check($otp, $select['otp'])) {
        return TRUE;
      }
      return FALSE;
    }
    return FALSE;
  }

  /**
   * Checks if the OTP of a user has expired.
   */
  public function expire($uid) {
    $delete = $this->database->delete('email_login_otp')
      ->condition('uid', $uid)
      ->execute();
    return $delete;
  }

  /**
   * Checks if the user has enabled the 2FA.
   */
  public function isEnabled($uid) {
    $exists = $this->database->select('otp_settings', 'o')
      ->fields('o')
      ->condition('uid', $uid, '=')
      ->execute()
      ->fetchAssoc();
    return $exists ? $exists['enabled'] : FALSE;
  }

  /**
   * Store the user settings.
   */
  public function storeSettings(array $settings) {
    $exists = $this->database->select('otp_settings', 'o')
      ->fields('o')
      ->condition('uid', $settings['uid'], '=')
      ->execute();
    if ($exists->fetchAssoc()) {
      $update = $this->database->update('otp_settings')
        ->fields([
          'email' => $settings['email'],
          'enabled' => $settings['enabled'],
        ])
        ->condition('uid', $settings['uid'], '=')
        ->execute();
      return $update ?? TRUE;
    }
    $store_settings = $this->database->insert('otp_settings')
      ->fields($settings)
      ->execute();

    return $store_settings ?? TRUE;
  }

  /**
   * Returns the remaining expiration time.
   */
  public function getExpirationTime($uid) {
    $unixTime = $this->database->select('email_login_otp', 'o')
      ->fields('o', ['expiration'])
      ->condition('uid', $uid, '=')
      ->condition('otp', '', '!=')
      ->execute()
      ->fetchAssoc();
    if ($unixTime) {
      return $unixTime['expiration'];
    }
    return FALSE;
  }

  /**
   * Fetches a user value by given field name.
   */
  private function getUserField($field) {
    $query = $this->database->select('users_field_data', 'u')
      ->fields('u', [$field])
      ->condition('name', $this->username, '=')
      ->execute()
      ->fetchAssoc();
    return $query[$field];
  }

  /**
   * Fetches a value from the settings table by the given field.
   */
  private function getField($field) {
    $uid = $this->tempStorageFactory->get('email_login_otp')->get('uid');
    $query = $this->database->select('otp_settings', 'u')
      ->fields('u', [$field])
      ->condition('uid', $uid, '=')
      ->execute()
      ->fetchAssoc();
    return $query[$field];
  }

  /**
   * Checks if the OTP already exists for a user.
   */
  private function exists($uid) {
    $exists = $this->database->select('email_login_otp', 'u')
      ->fields('u')
      ->condition('uid', $uid, '=')
      ->execute()
      ->fetchAssoc();
    return $exists ?? TRUE;
  }

  /**
   * Generates a new OTP.
   */
  private function new($uid) {
    $resend_wait_time = $this->configFactory
                      ->get('email_login_otp.config')
                      ->get('resend_wait_time');
    // Use cryptographically secure random_int() instead of insecure rand().
    $human_readable_otp = random_int(100000, 999999);
    $this->database->insert('email_login_otp')->fields([
      'uid' => $uid,
      'otp' => $this->hasher->hash($human_readable_otp),
      'expiration' => strtotime("+$resend_wait_time minutes", time()),
    ])->execute();
    return $human_readable_otp;
  }

  /**
   * Updates the existing OTP.
   */
  private function update($uid) {
    // Use cryptographically secure random_int() instead of insecure rand().
    $human_readable_otp = random_int(100000, 999999);
    $this->database->update('email_login_otp')
      ->fields([
        'otp' => $this->hasher->hash($human_readable_otp),
        'expiration' => strtotime("+5 minutes", time()),
      ])
      ->condition('uid', $uid, '=')
      ->execute();
    return $human_readable_otp;
  }

  /**
   * Checks if OTP verification is allowed based on flood control.
   *
   * @param int $uid
   *   The user ID.
   * @param string $identifier
   *   Additional identifier (e.g., IP address).
   *
   * @return bool
   *   TRUE if verification is allowed, FALSE otherwise.
   */
  public function isOtpVerificationAllowed($uid, $identifier = NULL) {
    $config = $this->configFactory->get('email_login_otp.config');

    // Default flood control limits if not configured.
    $user_limit = $config->get('otp_user_limit') ?: 5;
    $user_window = $config->get('otp_user_window') ?: 3600; // 1 hour
    $ip_limit = $config->get('otp_ip_limit') ?: 10;
    $ip_window = $config->get('otp_ip_window') ?: 3600; // 1 hour

    // Check user-based flood control.
    if (!$this->flood->isAllowed('email_login_otp.failed_verification_user', $user_limit, $user_window, (string) $uid)) {
      return FALSE;
    }

    // Check IP-based flood control if identifier is provided.
    if ($identifier && !$this->flood->isAllowed('email_login_otp.failed_verification_ip', $ip_limit, $ip_window, $identifier)) {
      return FALSE;
    }

    return TRUE;
  }

  /**
   * Registers a failed OTP verification attempt.
   *
   * @param int $uid
   *   The user ID.
   * @param string $identifier
   *   Additional identifier (e.g., IP address).
   */
  public function registerFailedOtpVerification($uid, $identifier = NULL) {
    $config = $this->configFactory->get('email_login_otp.config');

    $user_window = $config->get('otp_user_window') ?: 3600; // 1 hour
    $ip_window = $config->get('otp_ip_window') ?: 3600; // 1 hour

    // Register failed attempt for user.
    $this->flood->register('email_login_otp.failed_verification_user', $user_window, (string) $uid);

    // Register failed attempt for IP if identifier is provided.
    if ($identifier) {
      $this->flood->register('email_login_otp.failed_verification_ip', $ip_window, $identifier);
    }
  }

  /**
   * Clears flood control entries for successful OTP verification.
   *
   * @param int $uid
   *   The user ID.
   * @param string $identifier
   *   Additional identifier (e.g., IP address).
   */
  public function clearOtpVerificationFlood($uid, $identifier = NULL) {
    // Clear user-based flood control.
    $this->flood->clear('email_login_otp.failed_verification_user', (string) $uid);

    // Clear IP-based flood control if identifier is provided.
    if ($identifier) {
      $this->flood->clear('email_login_otp.failed_verification_ip', $identifier);
    }
  }

}
