diff --git a/email_login_otp.install b/email_login_otp.install
index 6fac98c..22ad133 100644
--- a/email_login_otp.install
+++ b/email_login_otp.install
@@ -5,6 +5,8 @@
  * Install hooks for email_login_otp module.
  */
 
+use Drupal\Core\Database\Database;
+
 /**
  * Database schema.
  */
diff --git a/src/Form/OTPForm.php b/src/Form/OTPForm.php
index 8bb80be..5d0798f 100644
--- a/src/Form/OTPForm.php
+++ b/src/Form/OTPForm.php
@@ -9,6 +9,7 @@ use Drupal\Core\Ajax\AjaxResponse;
 use Drupal\Core\Ajax\ReplaceCommand;
 use Drupal\Core\Ajax\RedirectCommand;
 use Drupal\Core\Url;
+use Symfony\Component\HttpFoundation\RequestStack;
 
 /**
  * Class for bulding OTP Form.
@@ -50,6 +51,20 @@ class OtpForm extends FormBase {
    */
   protected $enityTypeManager;
 
+  /**
+   * The request stack.
+   *
+   * @var \Symfony\Component\HttpFoundation\RequestStack
+   */
+  protected $requestStack;
+
+  /**
+   * The flood service.
+   *
+   * @var \Drupal\Core\Flood\FloodInterface
+   */
+  protected $flood;
+
   /**
    * Create method to inject the dependencies/services.
    */
@@ -62,6 +77,8 @@ class OtpForm extends FormBase {
     $instance->messenger = $container->get('messenger');
     $instance->cacheService = $container->get('cache.render');
     $instance->enityTypeManager = $container->get('entity_type.manager');
+    $instance->requestStack = $container->get('request_stack');
+    $instance->flood = $container->get('flood');
 
     return $instance;
   }
@@ -115,8 +132,33 @@ class OtpForm extends FormBase {
     $tempstore = $this->tempStoreFactory->get('email_login_otp');
     $uid = $tempstore->get('uid');
     $value = $form_state->getValue('otp');
+
+    // Get client IP for flood control.
+    $request = $this->requestStack->getCurrentRequest();
+    $client_ip = $request ? $request->getClientIp() : '';
+
+    // Check flood control - limit OTP verification attempts.
+    // Allow 5 attempts per user per hour and 10 attempts per IP per hour.
+    if (!$this->flood->isAllowed('email_login_otp.failed_verification_user', 5, 3600, (string) $uid)) {
+      $form_state->setErrorByName('otp', $this->t('Too many failed OTP verification attempts for this account. Please try again later.'));
+      return;
+    }
+
+    if (!$this->flood->isAllowed('email_login_otp.failed_verification_ip', 10, 3600, $client_ip)) {
+      $form_state->setErrorByName('otp', $this->t('Too many failed OTP verification attempts from this IP address. Please try again later.'));
+      return;
+    }
+
+    // Verify the OTP.
     if ($this->otp->check($uid, $value) == FALSE) {
+      // Register failed attempt for flood control.
+      $this->flood->register('email_login_otp.failed_verification_user', 3600, (string) $uid);
+      $this->flood->register('email_login_otp.failed_verification_ip', 3600, $client_ip);
       $form_state->setErrorByName('otp', $this->t('Invalid or expired OTP.'));
+    } else {
+      // Clear flood control on successful verification.
+      $this->flood->clear('email_login_otp.failed_verification_user', (string) $uid);
+      $this->flood->clear('email_login_otp.failed_verification_ip', $client_ip);
     }
   }
 
diff --git a/src/Services/Otp.php b/src/Services/Otp.php
index dca89e6..bd76e73 100644
--- a/src/Services/Otp.php
+++ b/src/Services/Otp.php
@@ -237,7 +237,8 @@ class Otp {
     $resend_wait_time = $this->configFactory
                       ->get('email_login_otp.config')
                       ->get('resend_wait_time');
-    $human_readable_otp = rand(100000, 999999);
+    // Use cryptographically secure random_int() instead of insecure rand().
+    $human_readable_otp = random_int(100000, 999999);
     $this->database->insert('email_login_otp')->fields([
       'uid' => $uid,
       'otp' => $this->hasher->hash($human_readable_otp),
@@ -250,7 +251,8 @@ class Otp {
    * Updates the existing OTP.
    */
   private function update($uid) {
-    $human_readable_otp = rand(100000, 999999);
+    // Use cryptographically secure random_int() instead of insecure rand().
+    $human_readable_otp = random_int(100000, 999999);
     $this->database->update('email_login_otp')
       ->fields([
         'otp' => $this->hasher->hash($human_readable_otp),
