<?php

declare(strict_types=1);

namespace Drupal\lcl\Plugin\Field\FieldFormatter;

use Drupal\Core\Field\FieldItemListInterface;
use Dr<PERSON>al\Core\Field\FormatterBase;
use Dr<PERSON>al\Core\Form\FormStateInterface;
use Dr<PERSON>al\image\Plugin\Field\FieldFormatter\ImageFormatterBase;
use Drupal\image\Plugin\Field\FieldFormatter\ImageFormatter;
use Drupal\Core\File\FileUrlGeneratorInterface;
use Drupal\Core\Link;
use Drupal\Core\Url;
use Drupal\image\Entity\ImageStyle;
use Drupal\Core\Asset\LibraryDiscoveryInterface;
use Drupal\Core\Cache\Cache;
use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Extension\ModuleHandlerInterface;
use Drupal\Core\Field\FieldDefinitionInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Session\AccountInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Plugin implementation of the 'LC Lightbox' formatter.
 *
 * @FieldFormatter(
 *   id = "lc_lightbox",
 *   module = "lcl",
 *   label = @Translation("LC Lightbox"),
 *   field_types = {
 *     "image"
 *   }
 * )
 */
final class LightboxFormatter extends ImageFormatter implements ContainerFactoryPluginInterface {

  /**
   * The current user.
   *
   * @var \Drupal\Core\Session\AccountInterface
   */
  protected $currentUser;

  /**
   * The entity type manager service object.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Element attachment allowing library to be attached to pages.
   *
   * @var \Drupal\colorbox\ElementAttachmentInterface
   */
  protected $attachment;

  /**
   * Drupal\Core\Extension\ModuleHandlerInterface definition.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  private $moduleHandler;

  /**
   * Library discovery service.
   *
   * @var \Drupal\Core\Asset\LibraryDiscoveryInterface
   */
  private $libraryDiscovery;

  /**
   * Constructs an ImageFormatter object.
   *
   * @param string $plugin_id
   *   The plugin_id for the formatter.
   * @param mixed $plugin_definition
   *   The plugin implementation definition.
   * @param \Drupal\Core\Field\FieldDefinitionInterface $field_definition
   *   The definition of the field to which the formatter is associated.
   * @param array $settings
   *   The formatter settings.
   * @param string $label
   *   The formatter label display setting.
   * @param string $view_mode
   *   The view mode.
   * @param array $third_party_settings
   *   Any third party settings.
   * @param \Drupal\Core\Session\AccountInterface $current_user
   *   The current user.
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entity_type_manager
   *   The image style storage.
   * @param \Drupal\colorbox\ElementAttachmentInterface $attachment
   *   Allow the library to be attached to the page.
   * @param \Drupal\Core\Extension\ModuleHandlerInterface $moduleHandler
   *   Module handler services.
   * @param \Drupal\Core\Asset\LibraryDiscoveryInterface $libraryDiscovery
   *   Library discovery service.
   */
  public function __construct(
    $plugin_id,
    $plugin_definition,
    FieldDefinitionInterface $field_definition,
    array $settings,
    $label,
    $view_mode,
    array $third_party_settings,
    AccountInterface $current_user,
    EntityTypeManagerInterface $entity_type_manager,
    ModuleHandlerInterface $moduleHandler,
    LibraryDiscoveryInterface $libraryDiscovery,
    FileUrlGeneratorInterface $file_url_generator,
  ) {
    parent::__construct($plugin_id, $plugin_definition, $field_definition, $settings, $label, $view_mode, $third_party_settings, $current_user, $entity_type_manager->getStorage('image_style'), $file_url_generator);
    $this->currentUser = $current_user;
    $this->entityTypeManager = $entity_type_manager;
    $this->moduleHandler = $moduleHandler;
    $this->libraryDiscovery = $libraryDiscovery;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition) {
    return new static(
      $plugin_id,
      $plugin_definition,
      $configuration['field_definition'],
      $configuration['settings'],
      $configuration['label'],
      $configuration['view_mode'],
      $configuration['third_party_settings'],
      $container->get('current_user'),
      $container->get('entity_type.manager'),
      $container->get('module_handler'),
      $container->get('library.discovery'),
      $container->get('file_url_generator')
    );
  }

  /**
   * {@inheritdoc}
   */
  public static function defaultSettings(): array {
    $setting = [
      'lcl_image_style' => NULL,
      'lcl_profile' => NULL,
      'image_loading' => [
        'attribute' => 'lazy',
      ],
    ];
    return $setting + parent::defaultSettings();
  }
  
  /**
   * Returns LC Lightbox profiles.
   */
  private function getLclProfiles() {
    $profiles = [];
    $lcl_profiles = $this->entityTypeManager->getStorage('lc_lightbox_profile')->loadMultiple();

    foreach ($lcl_profiles as $id => $profile) {
      $profiles[$id] = $profile->label();
    }

    return $profiles;
  }
   
  /**
   * {@inheritdoc}
   */
  public function settingsForm(array $form, FormStateInterface $form_state): array {
    $elements = parent::settingsForm($form, $form_state);
    $image_styles = image_style_options(FALSE);
    $profiles = $this->getLclProfiles();
    $image_styles_link = Link::fromTextAndUrl(
      $this->t('Configure Image Styles'),
      Url::fromRoute('entity.image_style.collection')
    );
    $lcl_profiles_link = Link::fromTextAndUrl(
      $this->t('Configure LC Lightbox Profiles'),
      Url::fromRoute('entity.lc_lightbox_profile.collection')
    );
    $elements['lcl_profile'] = [
      '#title' => $this->t('LC Lightbox Profile'),
      '#type' => 'select',
      '#default_value' => $this->getSetting('lcl_profile'),
      '#empty_option' => $this->t('- Select -'),
      '#options' => $profiles,
      '#required' => TRUE,
      '#description' => $lcl_profiles_link->toRenderable() + [
        '#access' => $this->currentUser->hasPermission('administer lc_lightbox_profile'),
      ],
    ];
    $elements['lcl_image_style'] = [
      '#title' => $this->t('Image style for LC Lightbox'),
      '#type' => 'select',
      '#default_value' => $this->getSetting('lcl_image_style'),
      '#empty_option' => $this->t('None (original image)'),
      '#options' => $image_styles,
      '#description' => $image_styles_link->toRenderable() + [
        '#access' => $this->currentUser->hasPermission('administer image styles'),
      ],
    ];

    return $elements;
  }

  /**
   * {@inheritdoc}
   */
  public function settingsSummary(): array {
    $summary = parent::settingsSummary();
    $image_styles = image_style_options(FALSE);
    $profiles = $this->getLclProfiles();
    if ($this->getSetting('lcl_profile')) {
      $summary[] = $this->t('LC Lightbox Profile: @profile', ['@profile' => $profiles[$this->getSetting('lcl_profile')]]);
    }
    // Unset possible 'No defined styles' option.
    unset($image_styles['']);
    if (isset($image_styles[$this->getSetting('lcl_image_style')])) {
      $summary[] = $this->t('LCL Image style: @style', ['@style' => $image_styles[$this->getSetting('lcl_image_style')]]);
    }
    return $summary;
  }

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode): array {
    $elements = parent::viewElements($items, $langcode);
    $files = $this->getEntitiesToView($items, $langcode);
    // Early opt-out if the field is empty.
    if (empty($files)) {
      return $elements;
    }
    return $elements;
  }

}
