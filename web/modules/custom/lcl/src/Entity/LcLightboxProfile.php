<?php

declare(strict_types=1);

namespace Drupal\lcl\Entity;

use Drupal\Core\Config\Entity\ConfigEntityBase;
use <PERSON><PERSON>al\lcl\LcLightboxProfileInterface;

/**
 * Defines the lc lightbox profiles entity type.
 *
 * @ConfigEntityType(
 *   id = "lc_lightbox_profile",
 *   label = @Translation("LC Lightbox Profiles"),
 *   label_collection = @Translation("LC Lightbox Profiles"),
 *   label_singular = @Translation("lc lightbox profiles"),
 *   label_plural = @Translation("lc lightbox profiles"),
 *   label_count = @PluralTranslation(
 *     singular = "@count lc lightbox profiles",
 *     plural = "@count lc lightbox profiles",
 *   ),
 *   handlers = {
 *     "list_builder" = "Drupal\lcl\LcLightboxProfileListBuilder",
 *     "form" = {
 *       "add" = "Drupal\lcl\Form\LcLightboxProfileForm",
 *       "edit" = "Drupal\lcl\Form\LcLightboxProfileForm",
 *       "delete" = "Drupal\Core\Entity\EntityDeleteForm",
 *     },
 *   },
 *   config_prefix = "lc_lightbox_profile",
 *   admin_permission = "administer lc_lightbox_profile",
 *   links = {
 *     "collection" = "/admin/structure/lc-lightbox-profile",
 *     "add-form" = "/admin/structure/lc-lightbox-profile/add",
 *     "edit-form" = "/admin/structure/lc-lightbox-profile/{lc_lightbox_profile}",
 *     "delete-form" = "/admin/structure/lc-lightbox-profile/{lc_lightbox_profile}/delete",
 *   },
 *   entity_keys = {
 *     "id" = "id",
 *     "label" = "label",
 *     "uuid" = "uuid",
 *   },
 *   config_export = {
 *     "id",
 *     "label",
 *   },
 * )
 */
final class LcLightboxProfile extends ConfigEntityBase implements LcLightboxProfileInterface {

  /**
   * The example ID.
   */
  protected string $id;

  /**
   * The example label.
   */
  protected string $label;

}
